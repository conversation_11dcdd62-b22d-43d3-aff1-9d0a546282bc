<template>
	<view class="content">
		<view class="pay">
			<view class="money">
				<view class="time">支付剩余时间 {{time?time:'00:00'}}</view>
				<view class="price" v-if="type != 3">￥{{order.pay_amount || '0.00'}}</view>
				<view class="integral" v-else>
					<text>{{order.pay_amount || '0'}}</text>
					<view>积分</view>
				</view>
				
				<view class="coupon" v-if="type == 1">优惠券抵扣{{order.coupon_amount || '0'}}元</view>
				<view class="coupons" v-if="type == 1">
					<view class="xx">
						<image src="/static/coupon_tb.png" mode="widthFix"></image>
						<view>优惠券</view>
					</view>
					<text>-￥{{order.coupon_amount || '0.00'}}</text>
				</view>
			</view>
			<!-- <view class="voucher" v-if="order.discount_amount > 0">
				<view>兑换券</view>
				<text>-￥ {{order.discount_amount}}</text>
			</view> -->
			<view class="payway">
				<view class="title">选择支付方式</view>
				<view class="nr">
					<!-- #ifdef MP-WEIXIN -->
					<view class="xx" :class="payment=='wechat'?'active':''" @click="getPayment('wechat')" v-if="type != 3 && order.pay_amount > 0">
						<image src="/static/icon_fxwx.png" mode="aspectFit"></image>
						<view>微信支付</view>
					</view>
					<!-- #endif -->
					<view class="xx" :class="payment=='wallet'?'active':''" v-if="type != 3 && type != 5" @click="getPayment('wallet')">
						<image src="/static/icon_ye.png" mode="aspectFit"></image>
						<view>余额支付<text>（余额￥{{left_amount}}）</text></view>
					</view>
					<!-- #ifdef MP-ALIPAY -->
					<view class="xx" :class="payment=='alipay'?'active':''" @click="getPayment('alipay')" v-if="type != 3 && order.pay_amount > 0">
						<image src="/static/icon_zfb.png" mode="aspectFit"></image>
						<view>支付宝支付</view>
					</view>
					<!-- #endif -->
					<view class="xx" :class="payment=='score'?'active':''" v-if="type == 3" @click="getPayment('score')">
						<image src="/static/icon_jf.png" mode="aspectFit"></image>
						<view>积分支付<text>（剩余积分{{left_amount}}）</text></view>
					</view>
				</view>
			</view>
			<view class="btn" @click="goWallet" v-if="isWallet">余额充值</view>
			<view class="btn" @click="getPay">确认支付  {{type!=3?'￥' + order.pay_amount:order.pay_amount + '积分'}}</view>
		</view>
	</view>
</template>
	
<script>
	export default {
		data() {
			return {
				backgroud: '#fff',
				title: '收银台',
				
				
				time:'',// 倒计时
				timer: null, //定时器
				type: '',// 1柜子订单 2商城订单 3积分商品 5余额充值
				order: '',// 订单信息
				order_sn: '',// 订单号
				left_amount: '',// 剩余积分/余额
				
				payment:'',// 支付方式 wechat（微信支付）,alipay(支付宝支付),wallet(余额),score(积分支付),card套餐卡,xinyi心意
				
				isWallet: false,
				
				ps_tel: '',
			}
		},
		onLoad(option) {
			// 扫码进入
			if(option.q){
				let qrUrl = decodeURIComponent(option.q)
				console.log(qrUrl)
				let type = this.$util.getQueryString(qrUrl, 'type')
				let order_sn = this.$util.getQueryString(qrUrl, 'order_sn')
				this.$cache.updateCache('type', type)
				this.$cache.updateCache('order_sn', order_sn)
			}
			if(option.type){
				this.type = option.type
				this.order_sn = option.order_sn
				this.getOrder()
			}
			this.getPstel()
		},
		onShow() {
			if(this.$cache.fetchCache('type')){
				if (!this.$cache.fetchCache('token')) {
					uni.navigateTo({
						url: '/pages/index/login'
					})
					return
				}
				this.type = this.$cache.fetchCache('type')
				this.order_sn = this.$cache.fetchCache('order_sn')
				this.bindOrder()
				this.$cache.updateCache('type', '')
				this.$cache.updateCache('order_sn', '')
			}
		},
		onHide(){
			console.log('清除定时器1')
			clearInterval(this.timer);
			this.timer = null;
		},
		onUnload() {
			console.log('清除定时器2')
			clearInterval(this.timer);
			this.timer = null;
		},
		methods: {
			getPstel(){
				let that = this;
				let params = {};
				that.$api.getPstel(params).then(res => {
					const {
						code,
						data,
						msg,
						time
					} = res.data
					if (code == 1) {
						that.ps_tel = data
					}else{
						uni.showToast({
							icon: "none",
							title: msg,
							duration: 2000
						});
					}
				})
				.catch((err) => {
						
				})
			},
			// 绑定售货机订单
			bindOrder(){
				let that = this
				let params = {
					order_sn: that.order_sn,
				};
				that.$api.bindOrder(params).then(res => {
					const {
						code,
						data,
						msg
					} = res.data
					if (code == 1) {
						that.getOrder()
					}else{
						uni.showToast({
							icon: "none",
							title: msg,
							duration: 2000
						});
					}
				})
				.catch((err) => {
					
				})
			},
			// 订单数据
			getOrder(){
				let that = this
				let params = {
					order_sn: that.order_sn,
				};
				if(that.type == 1){
					that.$api.OrdercontM(params).then(res => {
						const {
							code,
							data,
							msg
						} = res.data
						if (code == 1) {
							that.time = data.order_close_time
							that.order = data.order
							that.left_amount = data.user_money
							if(data.user_money >= data.order.pay_amount){
								that.payment = 'wallet'
							}else{
								// #ifdef MP-WEIXIN
								that.payment = 'wechat'
								// #endif
								// #ifdef MP-ALIPAY 
								that.payment = 'alipay'
								// #endif
							}
							if(data.user_money < 5){
								that.isWallet = true
							}
							that.countdown()
						}else{
							uni.showToast({
								icon: "none",
								title: msg,
								duration: 2000
							});
						}
					})
					.catch((err) => {
						
					})
				}else if(that.type == 2){
					that.$api.Ordercont(params).then(res => {
						const {
							code,
							data,
							msg
						} = res.data
						if (code == 1) {
							that.time = data.order_close_time
							that.order = data.order
							that.left_amount = data.left_amount
							if(data.user_money >= data.order.pay_amount){
								that.payment = 'wallet'
							}else{
								// #ifdef MP-WEIXIN
								that.payment = 'wechat'
								// #endif
								// #ifdef MP-ALIPAY 
								that.payment = 'alipay'
								// #endif
							}
							if(data.user_money < 5){
								that.isWallet = true
							}
							that.countdown()
						}else{
							uni.showToast({
								icon: "none",
								title: msg,
								duration: 2000
							});
						}
					})
					.catch((err) => {
						
					})
				}else{
					that.$api.Ordercont(params).then(res => {
						const {
							code,
							data,
							msg
						} = res.data
						if (code == 1) {
							that.time = data.order_close_time
							that.order = data.order
							that.left_amount = data.left_amount
							that.countdown()
						}else{
							uni.showToast({
								icon: "none",
								title: msg,
								duration: 2000
							});
						}
					})
					.catch((err) => {
						
					})
				}
			},
			// 余额充值
			goWallet(){
				uni.navigateTo({
					url: '/pages/pagesMy/recharge'
				})
			},
			// 返回
			back() {
				clearInterval(this.timer);
				this.timer = null;
				uni.navigateBack()
			},
			// 倒计时
			countdown() {
				let that = this
				let time = that.time;
				that.timer = setInterval(function(){
					time = time - 1;
					var minute = parseInt(time / 60);
					var second = parseInt(time % 60);
					if(second < 10){
						second = '0' + second
					}
					that.time = minute + ':' + second
					if(time == 0) {
						clearInterval(that.timer);
						minute = second = "00";
						that.timer = null;
						uni.showToast({
							icon: "none",
							title: '支付超时',
							duration: 2000,
						});
						setTimeout(function() {
							if(that.type == 1){
								uni.navigateTo({
									url: '/pages/pagesMy/sale_order'
								})
							}else if(that.type == 5){
								uni.navigateTo({
									url: '/pages/pagesMy/balance'
								})
							}else{
								uni.navigateTo({
									url: '/pages/pagesMy/shop_order'
								})
							}
						}, 2000);
					}
				},1000);
			},
			// 选择支付方式
			getPayment(e){
				let that = this
				if(e == 'wallet'){
					if(that.order.pay_amount*1 > that.left_amount*1){
						uni.showToast({
							icon: "none",
							title: '余额不足',
							duration: 2000
						});
						return
					}else{
						that.payment = e
					}
				}else if(e == 'score'){
					if(that.order.pay_amount*1 > that.left_amount*1){
						uni.showToast({
							icon: "none",
							title: '剩余积分不足',
							duration: 2000
						});
						return
					}else{
						that.payment = e
					}
				}else{
					that.payment = e
				}
			},
			// 支付
			getPay(){
				let that = this
				if(!that.payment){
					uni.showToast({
						icon: "none",
						title: '请选择支付方式',
						duration: 2000
					});
					return
				}
				uni.showLoading({
					title: '支付中',
					mask: true,
				});
				clearInterval(that.timer);
				that.timer = null;
				let params = {
					order_sn: that.order_sn,
					payment: that.payment,
				};
				if(that.type == 1){
					// 柜子订单
					that.$api.createPayM(params).then(res => {
						const {
							code,
							data,
							msg
						} = res.data
						if (code == 1) {
							if(that.payment == 'wallet'){
								uni.hideLoading();
								clearInterval(that.timer);
								that.timer = null;
								uni.showToast({
									title: "支付成功",
									icon: "none",
									mask: true,
									duration: 2000,
								});
								uni.showModal({
									title: '如需配送请拨打',
									content: that.ps_tel,
									confirmText: '配送',
									success: function (res) {
										if (res.confirm) {
											that.getCall()
										} else if (res.cancel) {
											uni.navigateTo({
												url: 'pay_cabinet?money=' + that.order.pay_amount + '&order_sn=' + that.order_sn
											})
										}
									}
								});
							}else{
								that.paymentWechatHandler(data.pay_data)
							}
						}else{
							uni.hideLoading();
							uni.showToast({
								title: msg,
								icon: "none",
								mask: true,
								duration: 2000,
							});
							setTimeout(function() {
								uni.switchTab({
									url: '/pages/index/index'
								})
							}, 2000);
						}
					})
					.catch((err) => {
						
					})
				}else{
					that.$api.createPay(params).then(res => {
						const {
							code,
							data,
							msg
						} = res.data
						if (code == 1) {
							if(that.payment == 'wallet'){
								uni.hideLoading();
								uni.showToast({
									title: "支付成功",
									icon: "none",
									mask: true,
									duration: 2000,
								});
								setTimeout(function() {
									uni.navigateTo({
										url: 'pay?type=' + that.type + '&money=' + that.order.pay_amount + '&order_sn=' + that.order_sn + '&order_id=' + that.order.order_sn
									})
								}, 2000);
							}else{
								that.paymentWechatHandler(data.pay_data)
							}
						}else{
							uni.hideLoading();
							uni.showToast({
								icon: "none",
								title: msg,
								mask: true,
								duration: 2000,
							});
							setTimeout(function() {
								if(that.type == 5){
									uni.navigateTo({
										url: '/pages/pagesMy/balance'
									})
								}else{
									uni.navigateTo({
										url: '/pages/pagesMy/shop_order'
									})
								}
							}, 2000);
						}
					})
					.catch((err) => {
						
					})
				}
			},
			// 第三方支付
			paymentWechatHandler(paymentParams){
				let that = this
				if(that.payment == 'wechat'){
					uni.requestPayment({
						timeStamp: paymentParams.timeStamp,
						nonceStr: paymentParams.nonceStr,
						package: paymentParams.package,
						signType: paymentParams.signType,
						paySign: paymentParams.paySign,
						success: () => {
							uni.hideLoading();
							uni.showToast({
								title: "支付成功",
								icon: "none",
								mask: true,
								duration: 2000,
							});
							if(that.type == 1){
								uni.showModal({
									title: '如需配送请拨打',
									content: that.ps_tel,
									confirmText: '配送',
									success: function (res) {
										if (res.confirm) {
											that.getCall()
										} else if (res.cancel) {
											uni.navigateTo({
												url: 'pay_cabinet?money=' + that.order.pay_amount + '&order_sn=' + that.order_sn
											})
										}
									}
								});
							}else{
								setTimeout(function() {
									uni.navigateTo({
										url: 'pay?type=' + that.type + '&money=' + that.order.pay_amount + '&order_sn=' + that.order_sn + '&order_id=' + that.order.order_sn
									})
								}, 2000);
							}
					    },
						fail: (res) => {
							uni.hideLoading();
							uni.showToast({
								title: "支付失败",
								icon: "none",
								mask: true,
								duration: 2000,
							});
							setTimeout(function() {
								if(that.type == 1){
									uni.switchTab({
										url: '/pages/index/index'
									})
								}else if(that.type == 5){
									uni.navigateTo({
										url: '/pages/pagesMy/balance'
									})
								}else{
									uni.navigateTo({
										url: '/pages/pagesMy/shop_order'
									})
								}
							}, 2000);
						},
					});
				}else{
					uni.requestPayment({
						provider: 'alipay',
					    orderInfo: paymentParams.trade_no,
						success: (res) => {
							if(res && res.resultCode == '9000') {
								uni.hideLoading();
								uni.showToast({
									title: "支付成功",
									icon: "none",
									mask: true,
									duration: 2000,
								});
								if(that.type == 1){
									uni.showModal({
										title: '如需配送请拨打',
										content: that.ps_tel,
										confirmText: '配送',
										success: function (res) {
											if (res.confirm) {
												that.getCall()
											} else if (res.cancel) {
												uni.navigateTo({
													url: 'pay_cabinet?money=' + that.order.pay_amount + '&order_sn=' + that.order_sn
												})
											}
										}
									});
								}else{
									setTimeout(function() {
										uni.navigateTo({
											url: 'pay?type=' + that.type + '&money=' + that.order.pay_amount + '&order_sn=' + that.order_sn + '&order_id=' + that.order.order_sn
										})
									}, 2000)
								}
							}
						},
						fail: (res) => {
							uni.hideLoading();
							uni.showToast({
								title: "支付失败",
								icon: "none",
								mask: true,
								duration: 2000,
							});
							setTimeout(function() {
								if(that.type == 1){
									uni.navigateTo({
										url: '/pages/index/index'
									})
								}else if(that.type == 5){
									uni.navigateTo({
										url: '/pages/pagesMy/balance'
									})
								}else{
									uni.navigateTo({
										url: '/pages/pagesMy/shop_order'
									})
								}
							}, 2000);
						},
					});
				}
			},
			
			getCall() {
				let that = this
				uni.makePhoneCall({
					phoneNumber: that.ps_tel
				}).catch((res) => {
					
				})
			},
		}
	}
</script>

<style>
	.pay{
		padding: 20rpx 24rpx 0;
	}
	.pay .money{
		border-radius: 20rpx;
		text-align: center;
		background: #fff;
		padding: 40rpx 24rpx;
		margin-bottom: 20rpx;
	}
	.pay .money .time{
		font-size: 26rpx;
		line-height: 36rpx;
		color: #999;
		margin-bottom: 26rpx;
	}
	.pay .money .price{
		font-size: 68rpx;
		line-height: 96rpx;
		color: #161513;
		font-weight: bold;
	}
	.pay .money .integral{
		display: flex;
		flex-wrap: wrap;
		align-items: flex-end;
		justify-content: center;
	}
	.pay .money .integral text{
		font-size: 68rpx;
		line-height: 96rpx;
		color: #161513;
		font-weight: bold;
	}
	.pay .money .integral view{
		font-size: 30rpx;
		line-height: 74rpx;
		color: #161513;
		font-weight: bold;
	}
	.pay .money .coupon{
		font-size: 26rpx;
		line-height: 36rpx;
		color: #FC451E;
		margin-top: 10rpx;
	}
	.pay .money .coupons{
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		background: #F4F4F4;
		padding: 0 34rpx;
		margin-top: 72rpx;
	}
	.pay .money .coupons .xx{
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}
	.pay .money .coupons .xx image{
		width: 56rpx;
		height: auto;
	}
	.pay .money .coupons .xx view{
		font-size: 28rpx;
		line-height: 98rpx;
		color: #161513;
		margin-left: 18rpx;
	}
	.pay .money .coupons text{
		font-size: 28rpx;
		line-height: 98rpx;
		color: #FC451E;
		/* padding-right: 24rpx;
		background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAZCAYAAAABmx/yAAAAAXNSR0IArs4c6QAAAVtJREFUOE+d1CFPw0AUB/D3r2i/QhPMggEHiiBJMMjJyam9qxsOXHHDQUV7pwhBADNIDAkJCBIMweEwJMh+gx65pWtua9feqLvk/frvvd47pGm66Xnei9b6JoqiU3J8IKX8AbBh6ouimLhiA68ADK2gmJnPuoJhCpRSt0Q0mBdrrU+EEOdteAb/gytY4gci6lvJYyHEZVPyAizxIxEdzYuLohBRFKllXIOuuBEaLKV8BnBgJQ2Z+Xq+XglL/AZg39rzQAhxb9atsA13wnLPH0S0ayX3nWCWZT3P877tznbCJEmCIAi+iKhn77UVGuT7/ieAreXuroRtaGVXp9Opn+f5OxHtWEmCmasTVEuM49gPw/AVwF71s4HxaDRaOLML0BXVPlVK+QTg0GUuq8R1b4IZXBfNPlVKmQEQVvcmzNx52xn4CyAs4QUzHzdNfG2QlVLbWmsze3euyLzkD1hcoRoBBLhzAAAAAElFTkSuQmCC) no-repeat right center;
		background-size: 14rpx auto; */
	}
	.pay .voucher{
		background: #fff;
		border-radius: 20rpx;
		text-align: center;
		padding: 32rpx 0 30rpx;
		margin-bottom: 20rpx;
	}
	.pay .voucher{
		background: #fff;
		border-radius: 20rpx;
		margin-bottom: 10rpx;
		padding: 0 22rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}
	.pay .voucher view{
		font-size: 28rpx;
		line-height: 100rpx;
		color: #333;
	}
	.pay .voucher text{
		font-size: 26rpx;
		line-height: 100rpx;
		color: #FF2F00;
	}
	.pay .payway{
		background: #fff;
		border-radius: 20rpx;
		margin-bottom: 184rpx;
	}
	.pay .payway .title{
		font-size: 28rpx;
		line-height: 100rpx;
		color: #333;
		font-weight: bold;
		border-bottom: 1px solid #F6F6F6;
		padding: 0 22rpx;
	}
	.pay .payway .nr{
		padding: 0 22rpx;
	}
	.pay .payway .nr .xx{
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		border-bottom: 1px solid #F6F6F6;
		background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA2lJREFUWEfNV01oXFUUPucmeVCMEFzVlZXaSgloU2QmeedkU5TqSoJQ3VQQ7KatG6slKKK01LZUI90odKMLRaRCFv4uiovmnjc/i462ZGVtKqW2u1Asrcybeaec6UyYTKfNm3lp44PhDtxzvu+79557zzkIKT9VxXK5vCmO460AMAYANtrPvt8BoGLj4OBgJZ/PX0BETQONaYxE5CMA2KOqi0binGuRGTHUarWtSZI0RCHimKoOA8AXzHxoJfz7Coii6ClVnVXVU86542EY3loJ0OajKFoHANNJkrw8NDQ0lc/nF+7ld08BInIAAN5U1Slmnk9D3GlTLBafqdVqs4j4ORF92g2jqwAROWMLIaLpfog7fbz3x+1oiOj5zrm7BDTJTxLR16tB3sIQkTdUdRczb2/HXSague2PrdbKOxcgIp8AwD9ENNOaWxLQDLifiWjzaq68y3FcHBgY2D4xMXHJ5pYEiMh5VX2t34BLK3pubu5Z59xXRGTX9o4Au+eqmjDzwbRAWexExN6H/4joMNoLF0XRNUTckPaeZyE33/n5+eHFxcU/mflxLJVKm+M4/oGZn84K3Iu/iFwAgB3ovd8JAK8w86u9AGS1FZFTAPCdCTjinLsehuHRrKC9+Hvv30PER0zAL865E2EY/toLQFZb7/1LAPCWCbgaBMFYLpe7lhW0F/9yuby+Wq1W/hcC1vwIPgaAf5n5SC9bmNW2PQjX6hp+DwDfYrFY3FSv13960EmoS1L6S1VfaDzFInLFObfxYT3F3vtH7UUmoicaych7/4FzbjAMww+znm0afxE5rKo3LO6W0rH33krq18fHx8+lAenXplAobKvX6yeZ+bll9UCpVHoyjuPTzLyxX/A0ft77v4Mg4Fwud3mZgGZdsF9V1zPzu2nAerXx3n/mnLsUhuGJlm+3ovQ0AHxDRF/2SnA/exHZbVmXiF5st+talnvvf0PEs0T0zmqIsJUj4pZO8ruOoJ1MRN5W1X3WmExOTv7Rj5BmwM0652bat33FHWgZFAqFDUmSzALAjyMjI8dGR0dvpBFi9xwRp1V1RxAEU62A6+abtjl9X1X3IuJN64JV1ZrTShAENkK1WrUKdwwRt6mq/R9qNqcr5pdUAlrKRcSuaIOsSdQorU0MIpqYs0mSVJh5IW17fhu6dI9iqNFKkgAAAABJRU5ErkJggg==) no-repeat right center;
		background-size: 32rpx 32rpx;
	}
	.pay .payway .nr .xx:last-child{
		border: none;
	}
	.pay .payway .nr .xx.active{
		background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAiCAYAAAA6RwvCAAAAAXNSR0IArs4c6QAAA+BJREFUWEfFmGuIVGUYx///c86cdXMbSXcDqciki2Ds7Qz0pYLUoiBDv5jFopa0QVHZRc/sFkQQ28zKgisVFnQxKoMiFxRSTM0PfUg7s65kkpEgQkHtRruVMXMuT5wZZ/Y2Z87ZmbHeb4f3ef7P733e63OIObTlT0hTw5XuXaSsJHALgGsBXHdJ4gLACwI5K8LD2T/Vo6ff5F9R5RnF0OjJ3UaPLwuwikQsio8IbAJfiqK9Yr3Gb8J8KoIkeuVWcZ1XATxAIhL0zIAiEBBD4mkvZvp5JggoULzTzHaRyjsE9LDRROkXICfibc6kGz4sZ18WxDDtJIC+arMQBFbIjrLVSqkDM21mgRg97tMUbzDKKKu1EZEtVlqfFmMaSGfSXqEABwFo1QaJ6Oe4Hu4d7o8dLtqXQNqevXhNTI+dArEwoliNZhxzvWzrcP/8n32hEohh5t4jualG9Tm6y7vfpvTNJZA2M7tco3KKgDJHpVrNHVu09pE0T+czYpjOXlLW1Kpajb+An1opbR2NbrmCC50xAPOqEQryaZoHdN2hwDonOPGTVJK+KL9rLTS2ZddQUfbWE2JRE7CrW8WSFsJxgfWDDs7/FhxBPG8tE9uct6BId71ApkL4mq4HPLgjBATcRSNpHyRwTz1AykH07nFx5LuKU+OHPuCDnCGwLAzkpsUFix9/KW9ZAwQE+J6JpD0OIF4J5PZlxPYuNW8y+IWLT76ePsJaIHxNEfwRCeSxlQq6V00eMQP7J2FqhbiUgAkf5AcAN1fKSKMO7HxERfuSyavJhzk0IqXdUVyYEdfEzHBnmTDtYyDuDFsj5WBGJwTN8QKcvzuqhPCn5isaSWc3IRvCQPz+cjC1QuTXCPg+O8zsepXKniggRZjXH1XRen0hE54APR9H2qKBIVzxHuKNT43FF8yP/0qgISpMvBF4+3EVS68mUkMuPj8eek4ESvtPyPG/J1r+/0tPOGSltbV5kPatuXZVpfVfPwME8BzxWkfSDYVngN8MM/cRyYejTk897ETkAyutb/S1SiCtL/xzg65pwwAW1CNIuAbHXdtuGx5oPD8NxP/oMJ37VMg+EIXz/PI1xwNWZ1KxA8UQs8sJ036OxKy6o55M4skzVr++c6pm2QKr08w9qZA7LkNZ4XgiWzJp/Y2ZAwsuOZP2CgKfEbiqTtkY84B1mVTsSDm9ioW10SuL4dp9IDdUvbXFv4a4m6r6ktXHgNfMlF1TadSJZK4DYB8Ed0deyAIXxCHHlZ6T2/WTYVmd068G43lpFtVerZD3A1gKchFEmvNByFGI+NXAOU9kP93YPmuAo2EAxf5/AU6Eibh+lv02AAAAAElFTkSuQmCC) no-repeat right center;
		background-size: 32rpx 32rpx;
	}
	.pay .payway .nr .xx image{
		width: 48rpx;
		height: 48rpx;
	}
	.pay .payway .nr .xx view{
		font-size: 28rpx;
		line-height: 100rpx;
		color: #333;
		margin-left: 22rpx;
	}
	.pay .payway .nr .xx view text{
		font-size: 28rpx;
		line-height: 100rpx;
		color: #FC451E;
		display: inline-block;
	}
	.pay .btn{
		background: #3478FB;
		border-radius: 44rpx;
		font-size: 30rpx;
		line-height: 88rpx;
		color: #fff;
		margin-bottom: 20rpx;
		text-align: center;
	}
	.pay .btn:last-child{
		margin-bottom: 0;
	}
</style>
